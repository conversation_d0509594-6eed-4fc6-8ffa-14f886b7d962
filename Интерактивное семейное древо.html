<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Интерактивное семейное древо</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow: hidden;
        }

        .toolbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            z-index: 1000;
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
        }

        .btn.female {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            box-shadow: 0 4px 15px rgba(250, 112, 154, 0.3);
        }

        .btn.female:hover {
            box-shadow: 0 6px 20px rgba(250, 112, 154, 0.4);
        }

        .canvas {
            position: absolute;
            top: 80px;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: auto;
            background: 
                radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%);
        }

        .person-card {
            position: absolute;
            width: 200px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            cursor: move;
            transition: all 0.3s ease;
            border: 3px solid transparent;
        }

        .person-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .person-card.selected {
            border-color: #4facfe;
            box-shadow: 0 8px 32px rgba(79, 172, 254, 0.3);
        }

        .person-card.male {
            border-left: 5px solid #4facfe;
        }

        .person-card.female {
            border-left: 5px solid #fa709a;
        }

        .person-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .person-icon.male {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .person-icon.female {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .person-name {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .person-dates {
            font-size: 12px;
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 8px;
        }

        .person-role {
            font-size: 11px;
            color: #e74c3c;
            text-align: center;
            font-weight: bold;
            text-transform: uppercase;
        }

        .edit-form {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            z-index: 2000;
            min-width: 400px;
            display: none;
        }

        .edit-form h3 {
            margin-bottom: 20px;
            color: #2c3e50;
            text-align: center;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #34495e;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #4facfe;
        }

        .form-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 1500;
            display: none;
        }

        .connection-line {
            position: absolute;
            height: 2px;
            background: #3498db;
            transform-origin: left center;
            z-index: 1;
            pointer-events: none;
        }

        .info {
            color: #7f8c8d;
            font-size: 12px;
            margin-left: auto;
        }

        .save-load {
            display: flex;
            gap: 10px;
            margin-left: auto;
        }
    </style>
</head>
<body>
    <div class="toolbar">
        <button class="btn male" onclick="addPerson('male')">👨 Добавить мужчину</button>
        <button class="btn female" onclick="addPerson('female')">👩 Добавить женщину</button>
        <button class="btn" onclick="toggleConnectionMode()">🔗 Связать</button>
        <button class="btn" onclick="clearAll()">🗑️ Очистить</button>
        
        <div class="save-load">
            <button class="btn" onclick="saveData()">💾 Сохранить</button>
            <button class="btn" onclick="loadData()">📁 Загрузить</button>
            <input type="file" id="fileInput" accept=".json" style="display: none;" onchange="handleFileLoad(event)">
        </div>
        
        <div class="info">
            Двойной клик - редактировать | Перетаскивание - перемещать
        </div>
    </div>

    <div class="canvas" id="canvas"></div>

    <div class="overlay" id="overlay" onclick="closeEditForm()"></div>
    
    <div class="edit-form" id="editForm">
        <h3>Редактировать персону</h3>
        <div class="form-group">
            <label>Имя:</label>
            <input type="text" id="editName" placeholder="Введите имя">
        </div>
        <div class="form-group">
            <label>Даты жизни:</label>
            <input type="text" id="editDates" placeholder="например: 1950-2020">
        </div>
        <div class="form-group">
            <label>Роль:</label>
            <input type="text" id="editRole" placeholder="например: отец, мать, сын">
        </div>
        <div class="form-group">
            <label>Пол:</label>
            <select id="editGender">
                <option value="male">Мужской</option>
                <option value="female">Женский</option>
            </select>
        </div>
        <div class="form-buttons">
            <button class="btn" onclick="saveEdit()">Сохранить</button>
            <button class="btn" onclick="deleteCard()">Удалить</button>
            <button class="btn" onclick="closeEditForm()">Отмена</button>
        </div>
    </div>

    <script>
        let people = [];
        let connections = [];
        let draggedElement = null;
        let dragOffset = { x: 0, y: 0 };
        let connectionMode = false;
        let selectedCard = null;
        let editingCard = null;
        let nextId = 1;

        function addPerson(gender) {
            const canvas = document.getElementById('canvas');
            const card = createPersonCard({
                id: nextId++,
                name: 'Новый человек',
                dates: '',
                role: '',
                gender: gender,
                x: Math.random() * (canvas.clientWidth - 200) + 100,
                y: Math.random() * (canvas.clientHeight - 150) + 100
            });

            people.push(card.data);
            canvas.appendChild(card.element);
        }

        function createPersonCard(data) {
            const card = document.createElement('div');
            card.className = `person-card ${data.gender}`;
            card.style.left = data.x + 'px';
            card.style.top = data.y + 'px';
            card.dataset.id = data.id;

            const icon = data.gender === 'male' ? '👨' : '👩';

            card.innerHTML = `
                <div class="person-icon ${data.gender}">${icon}</div>
                <div class="person-name">${data.name}</div>
                <div class="person-dates">${data.dates}</div>
                <div class="person-role">${data.role}</div>
            `;

            // Перетаскивание
            card.addEventListener('mousedown', startDrag);

            // Двойной клик для редактирования
            card.addEventListener('dblclick', () => editCard(data.id));

            // Клик для выбора/связывания
            card.addEventListener('click', (e) => {
                e.stopPropagation();
                if (connectionMode) {
                    handleConnection(data.id);
                } else {
                    selectCard(data.id);
                }
            });

            return { element: card, data: data };
        }

        function startDrag(e) {
            if (connectionMode) return;

            draggedElement = e.target.closest('.person-card');
            const rect = draggedElement.getBoundingClientRect();
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;

            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', stopDrag);

            draggedElement.style.zIndex = 1000;
        }

        function drag(e) {
            if (!draggedElement) return;

            const canvas = document.getElementById('canvas');
            const canvasRect = canvas.getBoundingClientRect();

            let x = e.clientX - canvasRect.left - dragOffset.x;
            let y = e.clientY - canvasRect.top - dragOffset.y;

            // Ограничения по границам
            x = Math.max(0, Math.min(x, canvas.clientWidth - 200));
            y = Math.max(0, Math.min(y, canvas.clientHeight - 150));

            draggedElement.style.left = x + 'px';
            draggedElement.style.top = y + 'px';

            // Обновляем данные
            const id = parseInt(draggedElement.dataset.id);
            const person = people.find(p => p.id === id);
            if (person) {
                person.x = x;
                person.y = y;
            }

            updateConnections();
        }

        function stopDrag() {
            if (draggedElement) {
                draggedElement.style.zIndex = '';
                draggedElement = null;
            }
            document.removeEventListener('mousemove', drag);
            document.removeEventListener('mouseup', stopDrag);
        }

        function selectCard(id) {
            // Убираем выделение с других карточек
            document.querySelectorAll('.person-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Выделяем текущую
            const card = document.querySelector(`[data-id="${id}"]`);
            if (card) {
                card.classList.add('selected');
                selectedCard = id;
            }
        }

        function editCard(id) {
            const person = people.find(p => p.id === id);
            if (!person) return;

            editingCard = id;

            document.getElementById('editName').value = person.name;
            document.getElementById('editDates').value = person.dates;
            document.getElementById('editRole').value = person.role;
            document.getElementById('editGender').value = person.gender;

            document.getElementById('overlay').style.display = 'block';
            document.getElementById('editForm').style.display = 'block';
        }

        function saveEdit() {
            if (!editingCard) return;

            const person = people.find(p => p.id === editingCard);
            if (!person) return;

            person.name = document.getElementById('editName').value || 'Без имени';
            person.dates = document.getElementById('editDates').value;
            person.role = document.getElementById('editRole').value;
            person.gender = document.getElementById('editGender').value;

            // Обновляем карточку
            const card = document.querySelector(`[data-id="${editingCard}"]`);
            if (card) {
                card.className = `person-card ${person.gender}`;
                const icon = person.gender === 'male' ? '👨' : '👩';
                card.innerHTML = `
                    <div class="person-icon ${person.gender}">${icon}</div>
                    <div class="person-name">${person.name}</div>
                    <div class="person-dates">${person.dates}</div>
                    <div class="person-role">${person.role}</div>
                `;

                // Восстанавливаем обработчики событий
                card.addEventListener('mousedown', startDrag);
                card.addEventListener('dblclick', () => editCard(person.id));
                card.addEventListener('click', (e) => {
                    e.stopPropagation();
                    if (connectionMode) {
                        handleConnection(person.id);
                    } else {
                        selectCard(person.id);
                    }
                });
            }

            closeEditForm();
        }

        function deleteCard() {
            if (!editingCard) return;

            // Удаляем из массива
            people = people.filter(p => p.id !== editingCard);

            // Удаляем связи
            connections = connections.filter(c => c.from !== editingCard && c.to !== editingCard);

            // Удаляем элемент
            const card = document.querySelector(`[data-id="${editingCard}"]`);
            if (card) {
                card.remove();
            }

            updateConnections();
            closeEditForm();
        }

        function closeEditForm() {
            document.getElementById('overlay').style.display = 'none';
            document.getElementById('editForm').style.display = 'none';
            editingCard = null;
        }

        function toggleConnectionMode() {
            connectionMode = !connectionMode;
            const btn = event.target;
            if (connectionMode) {
                btn.textContent = '❌ Отменить связь';
                btn.style.background = 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)';
                document.body.style.cursor = 'crosshair';
            } else {
                btn.textContent = '🔗 Связать';
                btn.style.background = 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)';
                document.body.style.cursor = 'default';
                selectedCard = null;
                document.querySelectorAll('.person-card').forEach(card => {
                    card.classList.remove('selected');
                });
            }
        }

        let firstSelected = null;

        function handleConnection(id) {
            if (!firstSelected) {
                firstSelected = id;
                selectCard(id);
            } else if (firstSelected !== id) {
                // Создаем связь
                connections.push({ from: firstSelected, to: id });
                updateConnections();

                // Сбрасываем выбор
                firstSelected = null;
                document.querySelectorAll('.person-card').forEach(card => {
                    card.classList.remove('selected');
                });
            }
        }

        function updateConnections() {
            // Удаляем старые линии
            document.querySelectorAll('.connection-line').forEach(line => line.remove());

            // Рисуем новые
            connections.forEach(conn => {
                const fromCard = document.querySelector(`[data-id="${conn.from}"]`);
                const toCard = document.querySelector(`[data-id="${conn.to}"]`);

                if (fromCard && toCard) {
                    drawConnection(fromCard, toCard);
                }
            });
        }

        function drawConnection(from, to) {
            const fromRect = from.getBoundingClientRect();
            const toRect = to.getBoundingClientRect();
            const canvas = document.getElementById('canvas');
            const canvasRect = canvas.getBoundingClientRect();

            const fromX = fromRect.left + fromRect.width / 2 - canvasRect.left;
            const fromY = fromRect.bottom - canvasRect.top;
            const toX = toRect.left + toRect.width / 2 - canvasRect.left;
            const toY = toRect.top - canvasRect.top;

            const line = document.createElement('div');
            line.className = 'connection-line';

            const length = Math.sqrt(Math.pow(toX - fromX, 2) + Math.pow(toY - fromY, 2));
            const angle = Math.atan2(toY - fromY, toX - fromX) * 180 / Math.PI;

            line.style.width = length + 'px';
            line.style.left = fromX + 'px';
            line.style.top = fromY + 'px';
            line.style.transform = `rotate(${angle}deg)`;

            canvas.appendChild(line);
        }

        function clearAll() {
            if (confirm('Удалить все карточки и связи?')) {
                people = [];
                connections = [];
                document.getElementById('canvas').innerHTML = '';
                nextId = 1;
            }
        }

        function saveData() {
            const data = {
                people: people,
                connections: connections,
                nextId: nextId
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = 'семейное_древо.json';
            a.click();

            URL.revokeObjectURL(url);
        }

        function loadData() {
            document.getElementById('fileInput').click();
        }

        function handleFileLoad(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);

                    // Очищаем текущие данные
                    document.getElementById('canvas').innerHTML = '';

                    // Загружаем новые данные
                    people = data.people || [];
                    connections = data.connections || [];
                    nextId = data.nextId || 1;

                    // Создаем карточки
                    const canvas = document.getElementById('canvas');
                    people.forEach(person => {
                        const card = createPersonCard(person);
                        canvas.appendChild(card.element);
                    });

                    // Обновляем связи
                    updateConnections();

                } catch (error) {
                    alert('Ошибка при загрузке файла: ' + error.message);
                }
            };
            reader.readAsText(file);
        }

        // Клик по пустому месту убирает выделение
        document.getElementById('canvas').addEventListener('click', () => {
            if (!connectionMode) {
                selectedCard = null;
                document.querySelectorAll('.person-card').forEach(card => {
                    card.classList.remove('selected');
                });
            }
        });

        // Инициализация
        window.addEventListener('load', () => {
            // Добавляем пример карточек
            addPerson('male');
            addPerson('female');
        });
    </script>
</body>
</html>
