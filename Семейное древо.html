<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Семейное древо семьи Долотовых</title>
    <style>
        @page {
            size: A3 landscape;
            margin: 1cm;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            color: #333;
            overflow-x: auto;
        }
        
        .tree-container {
            width: 100%;
            min-width: 4500px;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .title {
            text-align: center;
            font-size: 36px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 50px;
            text-transform: uppercase;
            letter-spacing: 3px;
            border-top: 3px solid #34495e;
            border-bottom: 3px solid #34495e;
            padding: 20px 0;
        }
        
        .generation {
            display: flex;
            gap: 30px;
            margin: 60px 0;
            position: relative;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .family-branch {
            display: flex;
            flex-direction: column;
            align-items: center;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            min-width: 250px;
        }
        
        .parents {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            position: relative;
        }
        
        .children {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            max-width: 400px;
        }
        
        .person {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 5px;
            position: relative;
        }
        
        .person-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            margin-bottom: 5px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border: 3px solid white;
        }
        
        .person-icon.male {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .person-icon.female {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        
        .person-icon.me {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: 4px solid #f39c12;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.4);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .person-name {
            font-weight: bold;
            font-size: 8px;
            text-align: center;
            max-width: 70px;
            line-height: 1.1;
            margin-bottom: 2px;
        }
        
        .person-dates {
            font-size: 6px;
            color: #666;
            text-align: center;
        }
        
        .person-role {
            font-size: 6px;
            color: #e74c3c;
            font-weight: bold;
            text-transform: uppercase;
            text-align: center;
            margin-top: 2px;
        }
        
        .couple::before {
            content: "♥";
            position: absolute;
            left: 50%;
            top: 20px;
            transform: translateX(-50%);
            color: #e74c3c;
            font-size: 12px;
            background: white;
            padding: 0 3px;
            z-index: 10;
        }
        
        .tree-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .connection-line {
            stroke: #3498db;
            stroke-width: 2;
            fill: none;
        }
        
        .generation-label {
            position: absolute;
            left: -100px;
            top: 50%;
            transform: translateY(-50%);
            font-weight: bold;
            color: #7f8c8d;
            writing-mode: vertical-rl;
            text-orientation: mixed;
        }
        
        @media print {
            body { background: white; }
            .tree-container { 
                box-shadow: none; 
                border: 1px solid #ddd;
            }
        }
    </style>
</head>
<body>
    <div class="tree-container">
        <div class="title">СЕМЕЙНОЕ ДРЕВО ДОЛОТОВЫХ<br><span style="font-size: 20px; font-weight: normal;">Полная генеалогия семьи</span></div>
        
        <!-- Поколение 1: Основатели -->
        <div class="generation" id="gen1">
            <div class="generation-label">Поколение 1</div>
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="mikhail-dolotov">
                        <div class="person-icon male">👴</div>
                        <div class="person-name">Долотов Михаил</div>
                        <div class="person-role">Основатель</div>
                    </div>
                </div>
            </div>
            
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="petr-dolotov">
                        <div class="person-icon male">👴</div>
                        <div class="person-name">Долотов Петр</div>
                        <div class="person-role">Основатель</div>
                    </div>
                </div>
            </div>
            
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="vasiliy-dolotov">
                        <div class="person-icon male">👴</div>
                        <div class="person-name">Долотов Василий</div>
                        <div class="person-role">Основатель</div>
                    </div>
                </div>
            </div>
            
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="ivan-dolotov">
                        <div class="person-icon male">👴</div>
                        <div class="person-name">Долотов Иван</div>
                        <div class="person-role">Основатель</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Поколение 2: Их дети -->
        <div class="generation" id="gen2">
            <div class="generation-label">Поколение 2</div>
            <!-- Дочь Михаила -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="mikhail-parent">
                        <div class="person-icon male">👴</div>
                        <div class="person-name">Михаил Долотов</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="aleksandra-mikhaylovna">
                        <div class="person-icon female">👵</div>
                        <div class="person-name">Александра Михайловна</div>
                        <div class="person-dates">1882-1965</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Дочь Петра -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="petr-parent">
                        <div class="person-icon male">👴</div>
                        <div class="person-name">Петр Долотов</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="mariya-badanova">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Мария Баданова</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Дочь Василия -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="vasiliy-parent">
                        <div class="person-icon male">👴</div>
                        <div class="person-name">Василий Долотов</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="evdokiya-nikolaevna">
                        <div class="person-icon female">👵</div>
                        <div class="person-name">Евдокия Николаевна</div>
                        <div class="person-dates">14.03.1890-04.10.1982</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Поколение 3: Внуки основателей -->
        <div class="generation" id="gen3">
            <div class="generation-label">Поколение 3</div>
            <!-- Дети Александры Михайловны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="aleksandra-parent">
                        <div class="person-icon female">👵</div>
                        <div class="person-name">Александра Михайловна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="aleksandr-dedushka">
                        <div class="person-icon male">👴</div>
                        <div class="person-name">Александр Алексеевич</div>
                        <div class="person-dates">19.01.1924-15.04.1997</div>
                        <div class="person-role">Дедушка</div>
                    </div>
                    <div class="person" data-person="mikhail-alekseevich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Михаил Алексеевич</div>
                        <div class="person-dates">29.10.1911-17.11.1963</div>
                        <div class="person-role">Дядя</div>
                    </div>
                    <div class="person" data-person="mariya-alekseevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Мария Алексеевна</div>
                        <div class="person-role">Тетя</div>
                    </div>
                    <div class="person" data-person="anna-alekseevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Алексеевна</div>
                        <div class="person-role">Тетя</div>
                    </div>
                    <div class="person" data-person="ivan-alekseevich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Иван Алексеевич</div>
                        <div class="person-role">Дядя</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Марии Бадановой -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="mariya-badanova-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Мария Баданова</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="vasiliy-badanov">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Василий</div>
                        <div class="person-role">Сын</div>
                    </div>
                    <div class="person" data-person="pavel-badanov">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Павел</div>
                        <div class="person-role">Сын</div>
                    </div>
                    <div class="person" data-person="fedor-badanov">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Федор</div>
                        <div class="person-role">Сын</div>
                    </div>
                    <div class="person" data-person="nikolay-badanov">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Николай</div>
                        <div class="person-role">Сын</div>
                    </div>
                    <div class="person" data-person="mikhail-badanov">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Михаил</div>
                        <div class="person-role">Сын</div>
                    </div>
                    <div class="person" data-person="nadezhda-badanova">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Надежда</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Евдокии -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="evdokiya-parent">
                        <div class="person-icon female">👵</div>
                        <div class="person-name">Евдокия Николаевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="klavdiya-babushka">
                        <div class="person-icon female">👵</div>
                        <div class="person-name">Клавдия Александровна</div>
                        <div class="person-dates">26.08.1926-15.04.1991</div>
                        <div class="person-role">Бабушка</div>
                    </div>
                    <div class="person" data-person="vitaliy-aleksandrovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Виталий Александрович</div>
                        <div class="person-dates">27.02.1930-23.03.1993</div>
                        <div class="person-role">Дядя</div>
                    </div>
                    <div class="person" data-person="anna-aleksandrovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Александровна</div>
                        <div class="person-dates">24.11.1916-01.11.1981</div>
                        <div class="person-role">Тетя</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Поколение 4: Правнуки -->
        <div class="generation" id="gen4">
            <div class="generation-label">Поколение 4</div>
            <!-- Семья Александра и Клавдии -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="aleksandr-klavdiya-1">
                        <div class="person-icon male">👴</div>
                        <div class="person-name">Александр Алексеевич</div>
                        <div class="person-role">Дедушка</div>
                    </div>
                    <div class="person" data-person="klavdiya-aleksandrovna">
                        <div class="person-icon female">👵</div>
                        <div class="person-name">Клавдия Александровна</div>
                        <div class="person-role">Бабушка</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="aleksey-otets">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Алексей Александрович</div>
                        <div class="person-dates">08.01.1948-08.12.2011</div>
                        <div class="person-role">Отец</div>
                    </div>
                    <div class="person" data-person="tatyana-antyuganova">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Татьяна Александровна</div>
                        <div class="person-dates">17.08.1950</div>
                        <div class="person-role">Тетя</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Михаила и Анны -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="mikhail-anna-1">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Михаил Алексеевич</div>
                        <div class="person-role">Дядя</div>
                    </div>
                    <div class="person" data-person="anna-mikhaylovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Михайловна</div>
                        <div class="person-dates">26.10.1912-06.07.2010</div>
                        <div class="person-role">Жена</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="vladimir-mikhaylovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Владимир Михайлович</div>
                        <div class="person-dates">16.03.1937-17.04.1960</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Виталия и Клавдии -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="vitaliy-klavdiya-1">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Виталий Александрович</div>
                        <div class="person-role">Дядя</div>
                    </div>
                    <div class="person" data-person="klavdiya-ivanovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Клавдия Ивановна</div>
                        <div class="person-dates">15.10.1926-30.08.2013</div>
                        <div class="person-role">Жена</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="anna-vitalevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Витальевна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="kapitalina-vitalevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Капиталина Витальевна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="mariya-vitalevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Мария Витальевна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Марии Алексеевны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="mariya-alekseevna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Мария Алексеевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="aleksandra-marievna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Александра</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="galina-nikolaevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Галина Николаевна</div>
                        <div class="person-dates">28.12.1941-08.05.2015</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Анны Алексеевны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="anna-alekseevna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Алексеевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="stanislav">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Станислав</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Ивана Алексеевича -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="ivan-alekseevich-parent">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Иван Алексеевич</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="valentina-ivanovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Валентина</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="aleksandr-ivanovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Александр</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Поколение 5: Твое поколение -->
        <div class="generation" id="gen5">
            <div class="generation-label">Поколение 5</div>
            <!-- Твоя семья -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="aleksey-tatyana-1">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Алексей Александрович</div>
                        <div class="person-role">Папа</div>
                    </div>
                    <div class="person" data-person="tatyana-alekseevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Татьяна Алексеевна</div>
                        <div class="person-dates">12.11.1947-11.05.1999</div>
                        <div class="person-role">Мама</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="aleksandr-brat">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Александр Алексеевич</div>
                        <div class="person-dates">30.04.1978</div>
                        <div class="person-role">Брат</div>
                    </div>
                    <div class="person" data-person="inna-ya">
                        <div class="person-icon female me">👩</div>
                        <div class="person-name">Инна Алексеевна</div>
                        <div class="person-dates">10.05.1982</div>
                        <div class="person-role">Я</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Татьяны и Владимира -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="tatyana-vladimir-1">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Татьяна Александровна</div>
                        <div class="person-role">Тетя</div>
                    </div>
                    <div class="person" data-person="vladimir-antyuganov">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Владимир Антюганов</div>
                        <div class="person-role">Дядя</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="svetlana-vladimirovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Светлана Владимировна</div>
                        <div class="person-dates">03.11.1975</div>
                        <div class="person-role">Двоюродная сестра</div>
                    </div>
                    <div class="person" data-person="andrey-vladimirovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Андрей Владимирович</div>
                        <div class="person-dates">28.01.1978</div>
                        <div class="person-role">Двоюродный брат</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Владимира Михайловича -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="vladimir-mikhaylovich-parent">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Владимир Михайлович</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="nina-vladimirovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Нина</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Галины и Ивана -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="galina-ivan-1">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Галина Николаевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                    <div class="person" data-person="ivan-gerasimovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Иван Герасимович</div>
                        <div class="person-dates">06.09.1936-03.10.2018</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="anna-galinovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Поколение 6: Внуки -->
        <div class="generation" id="gen6">
            <div class="generation-label">Поколение 6</div>
            <!-- Дети Нины -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="nina-vladimirovna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Нина</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="vadim-ninovich">
                        <div class="person-icon male">👶</div>
                        <div class="person-name">Вадим</div>
                        <div class="person-role">Сын</div>
                    </div>
                    <div class="person" data-person="anton-ninovich">
                        <div class="person-icon male">👶</div>
                        <div class="person-name">Антон</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Анны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="anna-galinovna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="mariya-annovna">
                        <div class="person-icon female">👶</div>
                        <div class="person-name">Мария</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="anton-annovich">
                        <div class="person-icon male">👶</div>
                        <div class="person-name">Антон</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- SVG для линий -->
        <svg class="tree-svg" id="tree-svg"></svg>
        
        <div style="margin-top: 60px; text-align: center; color: #7f8c8d; font-size: 14px; border-top: 2px solid #ecf0f1; padding-top: 20px;">
            Семейное древо составлено в 2025 году
        </div>
    </div>
    
    <script>
        function drawConnections() {
            const svg = document.getElementById('tree-svg');
            svg.innerHTML = '';
            
            function getPosition(selector) {
                const el = document.querySelector(`[data-person="${selector}"]`);
                if (!el) return null;
                const rect = el.getBoundingClientRect();
                const containerRect = document.querySelector('.tree-container').getBoundingClientRect();
                return {
                    x: rect.left + rect.width / 2 - containerRect.left,
                    y: rect.top + rect.height / 2 - containerRect.top
                };
            }
            
            function drawLine(x1, y1, x2, y2) {
                const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                line.setAttribute('x1', x1);
                line.setAttribute('y1', y1);
                line.setAttribute('x2', x2);
                line.setAttribute('y2', y2);
                line.setAttribute('class', 'connection-line');
                svg.appendChild(line);
            }
            
            // Рисуем линии внутри семейных блоков
            document.querySelectorAll('.family-branch').forEach(branch => {
                const parents = branch.querySelector('.parents');
                const children = branch.querySelector('.children');
                
                if (parents && children) {
                    const parentRect = parents.getBoundingClientRect();
                    const childrenRect = children.getBoundingClientRect();
                    const containerRect = document.querySelector('.tree-container').getBoundingClientRect();
                    
                    const parentX = parentRect.left + parentRect.width / 2 - containerRect.left;
                    const parentY = parentRect.bottom - containerRect.top;
                    const childrenX = childrenRect.left + childrenRect.width / 2 - containerRect.left;
                    const childrenY = childrenRect.top - containerRect.top;
                    
                    // Вертикальная линия от родителей к детям
                    drawLine(parentX, parentY, parentX, parentY + 20);
                    drawLine(parentX, parentY + 20, childrenX, parentY + 20);
                    drawLine(childrenX, parentY + 20, childrenX, childrenY);
                }
            });
        }
        
        window.onload = function() {
            setTimeout(drawConnections, 100);
            
            const button = document.createElement('button');
            button.textContent = 'Сохранить в PDF';
            button.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #3498db;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: bold;
                z-index: 1000;
                box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
            `;
            button.onclick = () => window.print();
            document.body.appendChild(button);
            
            window.addEventListener('resize', () => setTimeout(drawConnections, 100));
        };
    </script>
</body>
</html>
                        <div class="person-role">Дядя</div>
                    </div>
                    <div class="person" data-person="anna-mikhaylovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Михайловна</div>
                        <div class="person-dates">26.10.1912-06.07.2010</div>
                        <div class="person-role">Жена</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="vladimir-mikhaylovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Владимир Михайлович</div>
                        <div class="person-dates">16.03.1937-17.04.1960</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Виталия и Клавдии -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="vitaliy-klavdiya-1">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Виталий Александрович</div>
                        <div class="person-role">Дядя</div>
                    </div>
                    <div class="person" data-person="klavdiya-ivanovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Клавдия Ивановна</div>
                        <div class="person-dates">15.10.1926-30.08.2013</div>
                        <div class="person-role">Жена</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="anna-vitalevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Витальевна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="kapitalina-vitalevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Капиталина Витальевна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="mariya-vitalevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Мария Витальевна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Марии Алексеевны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="mariya-alekseevna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Мария Алексеевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="aleksandra-marievna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Александра</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="galina-nikolaevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Галина Николаевна</div>
                        <div class="person-dates">28.12.1941-08.05.2015</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Анны Алексеевны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="anna-alekseevna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Алексеевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="stanislav">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Станислав</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Ивана Алексеевича -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="ivan-alekseevich-parent">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Иван Алексеевич</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="valentina-ivanovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Валентина</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="aleksandr-ivanovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Александр</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Поколение 5: Твое поколение -->
        <div class="generation" id="gen5">
            <div class="generation-label">Поколение 5</div>
            <!-- Твоя семья -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="aleksey-tatyana-1">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Алексей Александрович</div>
                        <div class="person-role">Папа</div>
                    </div>
                    <div class="person" data-person="tatyana-alekseevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Татьяна Алексеевна</div>
                        <div class="person-dates">12.11.1947-11.05.1999</div>
                        <div class="person-role">Мама</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="aleksandr-brat">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Александр Алексеевич</div>
                        <div class="person-dates">30.04.1978</div>
                        <div class="person-role">Брат</div>
                    </div>
                    <div class="person" data-person="inna-ya">
                        <div class="person-icon female me">👩</div>
                        <div class="person-name">Инна Алексеевна</div>
                        <div class="person-dates">10.05.1982</div>
                        <div class="person-role">Я</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Татьяны и Владимира -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="tatyana-vladimir-1">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Татьяна Александровна</div>
                        <div class="person-role">Тетя</div>
                    </div>
                    <div class="person" data-person="vladimir-antyuganov">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Владимир Антюганов</div>
                        <div class="person-role">Дядя</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="svetlana-vladimirovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Светлана Владимировна</div>
                        <div class="person-dates">03.11.1975</div>
                        <div class="person-role">Двоюродная сестра</div>
                    </div>
                    <div class="person" data-person="andrey-vladimirovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Андрей Владимирович</div>
                        <div class="person-dates">28.01.1978</div>
                        <div class="person-role">Двоюродный брат</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Владимира Михайловича -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="vladimir-mikhaylovich-parent">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Владимир Михайлович</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="nina-vladimirovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Нина</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Галины и Ивана -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="galina-ivan-1">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Галина Николаевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                    <div class="person" data-person="ivan-gerasimovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Иван Герасимович</div>
                        <div class="person-dates">06.09.1936-03.10.2018</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="anna-galinovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Поколение 6: Внуки -->
        <div class="generation" id="gen6">
            <div class="generation-label">Поколение 6</div>
            <!-- Дети Нины -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="nina-vladimirovna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Нина</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="vadim-ninovich">
                        <div class="person-icon male">👶</div>
                        <div class="person-name">Вадим</div>
                        <div class="person-role">Сын</div>
                    </div>
                    <div class="person" data-person="anton-ninovich">
                        <div class="person-icon male">👶</div>
                        <div class="person-name">Антон</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Анны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="anna-galinovna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="mariya-annovna">
                        <div class="person-icon female">👶</div>
                        <div class="person-name">Мария</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="anton-annovich">
                        <div class="person-icon male">👶</div>
                        <div class="person-name">Антон</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- SVG для линий -->
        <svg class="tree-svg" id="tree-svg"></svg>
        
        <div style="margin-top: 60px; text-align: center; color: #7f8c8d; font-size: 14px; border-top: 2px solid #ecf0f1; padding-top: 20px;">
            Семейное древо составлено в 2025 году
        </div>
    </div>
    
    <script>
        function drawConnections() {
            const svg = document.getElementById('tree-svg');
            svg.innerHTML = '';
            
            function getPosition(selector) {
                const el = document.querySelector(`[data-person="${selector}"]`);
                if (!el) return null;
                const rect = el.getBoundingClientRect();
                const containerRect = document.querySelector('.tree-container').getBoundingClientRect();
                return {
                    x: rect.left + rect.width / 2 - containerRect.left,
                    y: rect.top + rect.height / 2 - containerRect.top
                };
            }
            
            function drawLine(x1, y1, x2, y2) {
                const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                line.setAttribute('x1', x1);
                line.setAttribute('y1', y1);
                line.setAttribute('x2', x2);
                line.setAttribute('y2', y2);
                line.setAttribute('class', 'connection-line');
                svg.appendChild(line);
            }
            
            // Рисуем линии внутри семейных блоков
            document.querySelectorAll('.family-branch').forEach(branch => {
                const parents = branch.querySelector('.parents');
                const children = branch.querySelector('.children');
                
                if (parents && children) {
                    const parentRect = parents.getBoundingClientRect();
                    const childrenRect = children.getBoundingClientRect();
                    const containerRect = document.querySelector('.tree-container').getBoundingClientRect();
                    
                    const parentX = parentRect.left + parentRect.width / 2 - containerRect.left;
                    const parentY = parentRect.bottom - containerRect.top;
                    const childrenX = childrenRect.left + childrenRect.width / 2 - containerRect.left;
                    const childrenY = childrenRect.top - containerRect.top;
                    
                    // Вертикальная линия от родителей к детям
                    drawLine(parentX, parentY, parentX, parentY + 20);
                    drawLine(parentX, parentY + 20, childrenX, parentY + 20);
                    drawLine(childrenX, parentY + 20, childrenX, childrenY);
                }
            });
        }
        
        window.onload = function() {
            setTimeout(drawConnections, 100);
            
            const button = document.createElement('button');
            button.textContent = 'Сохранить в PDF';
            button.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #3498db;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: bold;
                z-index: 1000;
                box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
            `;
            button.onclick = () => window.print();
            document.body.appendChild(button);
            
            window.addEventListener('resize', () => setTimeout(drawConnections, 100));
        };
    </script>
</body>
</html>
                        <div class="person-role">Дядя</div>
                    </div>
                    <div class="person" data-person="anna-mikhaylovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Михайловна</div>
                        <div class="person-dates">26.10.1912-06.07.2010</div>
                        <div class="person-role">Жена</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="vladimir-mikhaylovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Владимир Михайлович</div>
                        <div class="person-dates">16.03.1937-17.04.1960</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Виталия и Клавдии -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="vitaliy-klavdiya-1">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Виталий Александрович</div>
                        <div class="person-role">Дядя</div>
                    </div>
                    <div class="person" data-person="klavdiya-ivanovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Клавдия Ивановна</div>
                        <div class="person-dates">15.10.1926-30.08.2013</div>
                        <div class="person-role">Жена</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="anna-vitalevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Витальевна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="kapitalina-vitalevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Капиталина Витальевна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="mariya-vitalevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Мария Витальевна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Марии Алексеевны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="mariya-alekseevna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Мария Алексеевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="aleksandra-marievna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Александра</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="galina-nikolaevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Галина Николаевна</div>
                        <div class="person-dates">28.12.1941-08.05.2015</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Анны Алексеевны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="anna-alekseevna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Алексеевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="stanislav">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Станислав</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Ивана Алексеевича -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="ivan-alekseevich-parent">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Иван Алексеевич</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="valentina-ivanovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Валентина</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="aleksandr-ivanovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Александр</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Поколение 5: Твое поколение -->
        <div class="generation" id="gen5">
            <div class="generation-label">Поколение 5</div>
            <!-- Твоя семья -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="aleksey-tatyana-1">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Алексей Александрович</div>
                        <div class="person-role">Папа</div>
                    </div>
                    <div class="person" data-person="tatyana-alekseevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Татьяна Алексеевна</div>
                        <div class="person-dates">12.11.1947-11.05.1999</div>
                        <div class="person-role">Мама</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="aleksandr-brat">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Александр Алексеевич</div>
                        <div class="person-dates">30.04.1978</div>
                        <div class="person-role">Брат</div>
                    </div>
                    <div class="person" data-person="inna-ya">
                        <div class="person-icon female me">👩</div>
                        <div class="person-name">Инна Алексеевна</div>
                        <div class="person-dates">10.05.1982</div>
                        <div class="person-role">Я</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Татьяны и Владимира -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="tatyana-vladimir-1">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Татьяна Александровна</div>
                        <div class="person-role">Тетя</div>
                    </div>
                    <div class="person" data-person="vladimir-antyuganov">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Владимир Антюганов</div>
                        <div class="person-role">Дядя</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="svetlana-vladimirovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Светлана Владимировна</div>
                        <div class="person-dates">03.11.1975</div>
                        <div class="person-role">Двоюродная сестра</div>
                    </div>
                    <div class="person" data-person="andrey-vladimirovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Андрей Владимирович</div>
                        <div class="person-dates">28.01.1978</div>
                        <div class="person-role">Двоюродный брат</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Владимира Михайловича -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="vladimir-mikhaylovich-parent">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Владимир Михайлович</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="nina-vladimirovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Нина</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Галины и Ивана -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="galina-ivan-1">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Галина Николаевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                    <div class="person" data-person="ivan-gerasimovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Иван Герасимович</div>
                        <div class="person-dates">06.09.1936-03.10.2018</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="anna-galinovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Поколение 6: Внуки -->
        <div class="generation" id="gen6">
            <div class="generation-label">Поколение 6</div>
            <!-- Дети Нины -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="nina-vladimirovna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Нина</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="vadim-ninovich">
                        <div class="person-icon male">👶</div>
                        <div class="person-name">Вадим</div>
                        <div class="person-role">Сын</div>
                    </div>
                    <div class="person" data-person="anton-ninovich">
                        <div class="person-icon male">👶</div>
                        <div class="person-name">Антон</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Анны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="anna-galinovna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="mariya-annovna">
                        <div class="person-icon female">👶</div>
                        <div class="person-name">Мария</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="anton-annovich">
                        <div class="person-icon male">👶</div>
                        <div class="person-name">Антон</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- SVG для линий -->
        <svg class="tree-svg" id="tree-svg"></svg>
        
        <div style="margin-top: 60px; text-align: center; color: #7f8c8d; font-size: 14px; border-top: 2px solid #ecf0f1; padding-top: 20px;">
            Семейное древо составлено в 2025 году
        </div>
    </div>
    
    <script>
        function drawConnections() {
            const svg = document.getElementById('tree-svg');
            svg.innerHTML = '';
            
            function getPosition(selector) {
                const el = document.querySelector(`[data-person="${selector}"]`);
                if (!el) return null;
                const rect = el.getBoundingClientRect();
                const containerRect = document.querySelector('.tree-container').getBoundingClientRect();
                return {
                    x: rect.left + rect.width / 2 - containerRect.left,
                    y: rect.top + rect.height / 2 - containerRect.top
                };
            }
            
            function drawLine(x1, y1, x2, y2) {
                const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                line.setAttribute('x1', x1);
                line.setAttribute('y1', y1);
                line.setAttribute('x2', x2);
                line.setAttribute('y2', y2);
                line.setAttribute('class', 'connection-line');
                svg.appendChild(line);
            }
            
            // Рисуем линии внутри семейных блоков
            document.querySelectorAll('.family-branch').forEach(branch => {
                const parents = branch.querySelector('.parents');
                const children = branch.querySelector('.children');
                
                if (parents && children) {
                    const parentRect = parents.getBoundingClientRect();
                    const childrenRect = children.getBoundingClientRect();
                    const containerRect = document.querySelector('.tree-container').getBoundingClientRect();
                    
                    const parentX = parentRect.left + parentRect.width / 2 - containerRect.left;
                    const parentY = parentRect.bottom - containerRect.top;
                    const childrenX = childrenRect.left + childrenRect.width / 2 - containerRect.left;
                    const childrenY = childrenRect.top - containerRect.top;
                    
                    // Вертикальная линия от родителей к детям
                    drawLine(parentX, parentY, parentX, parentY + 20);
                    drawLine(parentX, parentY + 20, childrenX, parentY + 20);
                    drawLine(childrenX, parentY + 20, childrenX, childrenY);
                }
            });
        }
        
        window.onload = function() {
            setTimeout(drawConnections, 100);
            
            const button = document.createElement('button');
            button.textContent = 'Сохранить в PDF';
            button.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #3498db;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: bold;
                z-index: 1000;
                box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
            `;
            button.onclick = () => window.print();
            document.body.appendChild(button);
            
            window.addEventListener('resize', () => setTimeout(drawConnections, 100));
        };
    </script>
</body>
</html>
                        <div class="person-role">Дядя</div>
                    </div>
                    <div class="person" data-person="anna-mikhaylovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Михайловна</div>
                        <div class="person-dates">26.10.1912-06.07.2010</div>
                        <div class="person-role">Жена</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="vladimir-mikhaylovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Владимир Михайлович</div>
                        <div class="person-dates">16.03.1937-17.04.1960</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Виталия и Клавдии -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="vitaliy-klavdiya-1">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Виталий Александрович</div>
                        <div class="person-role">Дядя</div>
                    </div>
                    <div class="person" data-person="klavdiya-ivanovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Клавдия Ивановна</div>
                        <div class="person-dates">15.10.1926-30.08.2013</div>
                        <div class="person-role">Жена</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="anna-vitalevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Витальевна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="kapitalina-vitalevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Капиталина Витальевна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="mariya-vitalevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Мария Витальевна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Марии Алексеевны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="mariya-alekseevna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Мария Алексеевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="aleksandra-marievna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Александра</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="galina-nikolaevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Галина Николаевна</div>
                        <div class="person-dates">28.12.1941-08.05.2015</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Анны Алексеевны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="anna-alekseevna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Алексеевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="stanislav">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Станислав</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Ивана Алексеевича -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="ivan-alekseevich-parent">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Иван Алексеевич</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="valentina-ivanovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Валентина</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="aleksandr-ivanovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Александр</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Поколение 5: Твое поколение -->
        <div class="generation" id="gen5">
            <div class="generation-label">Поколение 5</div>
            <!-- Твоя семья -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="aleksey-tatyana-1">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Алексей Александрович</div>
                        <div class="person-role">Папа</div>
                    </div>
                    <div class="person" data-person="tatyana-alekseevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Татьяна Алексеевна</div>
                        <div class="person-dates">12.11.1947-11.05.1999</div>
                        <div class="person-role">Мама</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="aleksandr-brat">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Александр Алексеевич</div>
                        <div class="person-dates">30.04.1978</div>
                        <div class="person-role">Брат</div>
                    </div>
                    <div class="person" data-person="inna-ya">
                        <div class="person-icon female me">👩</div>
                        <div class="person-name">Инна Алексеевна</div>
                        <div class="person-dates">10.05.1982</div>
                        <div class="person-role">Я</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Татьяны и Владимира -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="tatyana-vladimir-1">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Татьяна Александровна</div>
                        <div class="person-role">Тетя</div>
                    </div>
                    <div class="person" data-person="vladimir-antyuganov">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Владимир Антюганов</div>
                        <div class="person-role">Дядя</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="svetlana-vladimirovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Светлана Владимировна</div>
                        <div class="person-dates">03.11.1975</div>
                        <div class="person-role">Двоюродная сестра</div>
                    </div>
                    <div class="person" data-person="andrey-vladimirovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Андрей Владимирович</div>
                        <div class="person-dates">28.01.1978</div>
                        <div class="person-role">Двоюродный брат</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Владимира Михайловича -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="vladimir-mikhaylovich-parent">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Владимир Михайлович</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="nina-vladimirovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Нина</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Галины и Ивана -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="galina-ivan-1">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Галина Николаевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                    <div class="person" data-person="ivan-gerasimovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Иван Герасимович</div>
                        <div class="person-dates">06.09.1936-03.10.2018</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="anna-galinovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Поколение 6: Внуки -->
        <div class="generation" id="gen6">
            <div class="generation-label">Поколение 6</div>
            <!-- Дети Нины -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="nina-vladimirovna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Нина</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="vadim-ninovich">
                        <div class="person-icon male">👶</div>
                        <div class="person-name">Вадим</div>
                        <div class="person-role">Сын</div>
                    </div>
                    <div class="person" data-person="anton-ninovich">
                        <div class="person-icon male">👶</div>
                        <div class="person-name">Антон</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Анны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="anna-galinovna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="mariya-annovna">
                        <div class="person-icon female">👶</div>
                        <div class="person-name">Мария</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="anton-annovich">
                        <div class="person-icon male">👶</div>
                        <div class="person-name">Антон</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- SVG для линий -->
        <svg class="tree-svg" id="tree-svg"></svg>
        
        <div style="margin-top: 60px; text-align: center; color: #7f8c8d; font-size: 14px; border-top: 2px solid #ecf0f1; padding-top: 20px;">
            Семейное древо составлено в 2025 году
        </div>
    </div>
    
    <script>
        function drawConnections() {
            const svg = document.getElementById('tree-svg');
            svg.innerHTML = '';
            
            function getPosition(selector) {
                const el = document.querySelector(`[data-person="${selector}"]`);
                if (!el) return null;
                const rect = el.getBoundingClientRect();
                const containerRect = document.querySelector('.tree-container').getBoundingClientRect();
                return {
                    x: rect.left + rect.width / 2 - containerRect.left,
                    y: rect.top + rect.height / 2 - containerRect.top
                };
            }
            
            function drawLine(x1, y1, x2, y2) {
                const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                line.setAttribute('x1', x1);
                line.setAttribute('y1', y1);
                line.setAttribute('x2', x2);
                line.setAttribute('y2', y2);
                line.setAttribute('class', 'connection-line');
                svg.appendChild(line);
            }
            
            // Рисуем линии внутри семейных блоков
            document.querySelectorAll('.family-branch').forEach(branch => {
                const parents = branch.querySelector('.parents');
                const children = branch.querySelector('.children');
                
                if (parents && children) {
                    const parentRect = parents.getBoundingClientRect();
                    const childrenRect = children.getBoundingClientRect();
                    const containerRect = document.querySelector('.tree-container').getBoundingClientRect();
                    
                    const parentX = parentRect.left + parentRect.width / 2 - containerRect.left;
                    const parentY = parentRect.bottom - containerRect.top;
                    const childrenX = childrenRect.left + childrenRect.width / 2 - containerRect.left;
                    const childrenY = childrenRect.top - containerRect.top;
                    
                    // Вертикальная линия от родителей к детям
                    drawLine(parentX, parentY, parentX, parentY + 20);
                    drawLine(parentX, parentY + 20, childrenX, parentY + 20);
                    drawLine(childrenX, parentY + 20, childrenX, childrenY);
                }
            });
        }
        
        window.onload = function() {
            setTimeout(drawConnections, 100);
            
            const button = document.createElement('button');
            button.textContent = 'Сохранить в PDF';
            button.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #3498db;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: bold;
                z-index: 1000;
                box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
            `;
            button.onclick = () => window.print();
            document.body.appendChild(button);
            
            window.addEventListener('resize', () => setTimeout(drawConnections, 100));
        };
    </script>
</body>
</html>
                        <div class="person-role">Дядя</div>
                    </div>
                    <div class="person" data-person="anna-mikhaylovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Михайловна</div>
                        <div class="person-dates">26.10.1912-06.07.2010</div>
                        <div class="person-role">Жена</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="vladimir-mikhaylovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Владимир Михайлович</div>
                        <div class="person-dates">16.03.1937-17.04.1960</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Виталия и Клавдии -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="vitaliy-klavdiya-1">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Виталий Александрович</div>
                        <div class="person-role">Дядя</div>
                    </div>
                    <div class="person" data-person="klavdiya-ivanovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Клавдия Ивановна</div>
                        <div class="person-dates">15.10.1926-30.08.2013</div>
                        <div class="person-role">Жена</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="anna-vitalevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Витальевна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="kapitalina-vitalevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Капиталина Витальевна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="mariya-vitalevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Мария Витальевна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Марии Алексеевны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="mariya-alekseevna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Мария Алексеевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="aleksandra-marievna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Александра</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="galina-nikolaevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Галина Николаевна</div>
                        <div class="person-dates">28.12.1941-08.05.2015</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Анны Алексеевны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="anna-alekseevna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Алексеевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="stanislav">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Станислав</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Ивана Алексеевича -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="ivan-alekseevich-parent">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Иван Алексеевич</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="valentina-ivanovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Валентина</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="aleksandr-ivanovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Александр</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Поколение 5: Твое поколение -->
        <div class="generation" id="gen5">
            <div class="generation-label">Поколение 5</div>
            <!-- Твоя семья -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="aleksey-tatyana-1">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Алексей Александрович</div>
                        <div class="person-role">Папа</div>
                    </div>
                    <div class="person" data-person="tatyana-alekseevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Татьяна Алексеевна</div>
                        <div class="person-dates">12.11.1947-11.05.1999</div>
                        <div class="person-role">Мама</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="aleksandr-brat">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Александр Алексеевич</div>
                        <div class="person-dates">30.04.1978</div>
                        <div class="person-role">Брат</div>
                    </div>
                    <div class="person" data-person="inna-ya">
                        <div class="person-icon female me">👩</div>
                        <div class="person-name">Инна Алексеевна</div>
                        <div class="person-dates">10.05.1982</div>
                        <div class="person-role">Я</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Татьяны и Владимира -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="tatyana-vladimir-1">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Татьяна Александровна</div>
                        <div class="person-role">Тетя</div>
                    </div>
                    <div class="person" data-person="vladimir-antyuganov">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Владимир Антюганов</div>
                        <div class="person-role">Дядя</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="svetlana-vladimirovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Светлана Владимировна</div>
                        <div class="person-dates">03.11.1975</div>
                        <div class="person-role">Двоюродная сестра</div>
                    </div>
                    <div class="person" data-person="andrey-vladimirovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Андрей Владимирович</div>
                        <div class="person-dates">28.01.1978</div>
                        <div class="person-role">Двоюродный брат</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Владимира Михайловича -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="vladimir-mikhaylovich-parent">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Владимир Михайлович</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="nina-vladimirovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Нина</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Галины и Ивана -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="galina-ivan-1">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Галина Николаевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                    <div class="person" data-person="ivan-gerasimovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Иван Герасимович</div>
                        <div class="person-dates">06.09.1936-03.10.2018</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="anna-galinovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Поколение 6: Внуки -->
        <div class="generation" id="gen6">
            <div class="generation-label">Поколение 6</div>
            <!-- Дети Нины -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="nina-vladimirovna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Нина</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="vadim-ninovich">
                        <div class="person-icon male">👶</div>
                        <div class="person-name">Вадим</div>
                        <div class="person-role">Сын</div>
                    </div>
                    <div class="person" data-person="anton-ninovich">
                        <div class="person-icon male">👶</div>
                        <div class="person-name">Антон</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Анны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="anna-galinovna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="mariya-annovna">
                        <div class="person-icon female">👶</div>
                        <div class="person-name">Мария</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="anton-annovich">
                        <div class="person-icon male">👶</div>
                        <div class="person-name">Антон</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- SVG для линий -->
        <svg class="tree-svg" id="tree-svg"></svg>
        
        <div style="margin-top: 60px; text-align: center; color: #7f8c8d; font-size: 14px; border-top: 2px solid #ecf0f1; padding-top: 20px;">
            Семейное древо составлено в 2025 году
        </div>
    </div>
    
    <script>
        function drawConnections() {
            const svg = document.getElementById('tree-svg');
            svg.innerHTML = '';
            
            function getPosition(selector) {
                const el = document.querySelector(`[data-person="${selector}"]`);
                if (!el) return null;
                const rect = el.getBoundingClientRect();
                const containerRect = document.querySelector('.tree-container').getBoundingClientRect();
                return {
                    x: rect.left + rect.width / 2 - containerRect.left,
                    y: rect.top + rect.height / 2 - containerRect.top
                };
            }
            
            function drawLine(x1, y1, x2, y2) {
                const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                line.setAttribute('x1', x1);
                line.setAttribute('y1', y1);
                line.setAttribute('x2', x2);
                line.setAttribute('y2', y2);
                line.setAttribute('class', 'connection-line');
                svg.appendChild(line);
            }
            
            // Рисуем линии внутри семейных блоков
            document.querySelectorAll('.family-branch').forEach(branch => {
                const parents = branch.querySelector('.parents');
                const children = branch.querySelector('.children');
                
                if (parents && children) {
                    const parentRect = parents.getBoundingClientRect();
                    const childrenRect = children.getBoundingClientRect();
                    const containerRect = document.querySelector('.tree-container').getBoundingClientRect();
                    
                    const parentX = parentRect.left + parentRect.width / 2 - containerRect.left;
                    const parentY = parentRect.bottom - containerRect.top;
                    const childrenX = childrenRect.left + childrenRect.width / 2 - containerRect.left;
                    const childrenY = childrenRect.top - containerRect.top;
                    
                    // Вертикальная линия от родителей к детям
                    drawLine(parentX, parentY, parentX, parentY + 20);
                    drawLine(parentX, parentY + 20, childrenX, parentY + 20);
                    drawLine(childrenX, parentY + 20, childrenX, childrenY);
                }
            });
        }
        
        window.onload = function() {
            setTimeout(drawConnections, 100);
            
            const button = document.createElement('button');
            button.textContent = 'Сохранить в PDF';
            button.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #3498db;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: bold;
                z-index: 1000;
                box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
            `;
            button.onclick = () => window.print();
            document.body.appendChild(button);
            
            window.addEventListener('resize', () => setTimeout(drawConnections, 100));
        };
    </script>
</body>
</html>
                        <div class="person-role">Дядя</div>
                    </div>
                    <div class="person" data-person="anna-mikhaylovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Михайловна</div>
                        <div class="person-dates">26.10.1912-06.07.2010</div>
                        <div class="person-role">Жена</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="vladimir-mikhaylovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Владимир Михайлович</div>
                        <div class="person-dates">16.03.1937-17.04.1960</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Виталия и Клавдии -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="vitaliy-klavdiya-1">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Виталий Александрович</div>
                        <div class="person-role">Дядя</div>
                    </div>
                    <div class="person" data-person="klavdiya-ivanovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Клавдия Ивановна</div>
                        <div class="person-dates">15.10.1926-30.08.2013</div>
                        <div class="person-role">Жена</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="anna-vitalevna">
                        <div class="person-icon female">�</div>
                        <div class="person-name">Анна Витальевна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="kapitalina-vitalevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Капиталина Витальевна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="mariya-vitalevna">
                        <div class="person-icon female">�</div>
                        <div class="person-name">Мария Витальевна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Марии Алексеевны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="mariya-alekseevna-parent">
                        <div class="person-icon female">�</div>
                        <div class="person-name">Мария Алексеевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="aleksandra-marievna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Александра</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="galina-nikolaevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Галина Николаевна</div>
                        <div class="person-dates">28.12.1941-08.05.2015</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Анны Алексеевны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="anna-alekseevna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна Алексеевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="stanislav">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Станислав</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Ивана Алексеевича -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="ivan-alekseevich-parent">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Иван Алексеевич</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="valentina-ivanovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Валентина</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="aleksandr-ivanovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Александр</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Поколение 5: Твое поколение -->
        <div class="generation" id="gen5">
            <div class="generation-label">Поколение 5</div>
            <!-- Твоя семья -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="aleksey-tatyana-1">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Алексей Александрович</div>
                        <div class="person-role">Папа</div>
                    </div>
                    <div class="person" data-person="tatyana-alekseevna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Татьяна Алексеевна</div>
                        <div class="person-dates">12.11.1947-11.05.1999</div>
                        <div class="person-role">Мама</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="aleksandr-brat">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Александр Алексеевич</div>
                        <div class="person-dates">30.04.1978</div>
                        <div class="person-role">Брат</div>
                    </div>
                    <div class="person" data-person="inna-ya">
                        <div class="person-icon female me">👩</div>
                        <div class="person-name">Инна Алексеевна</div>
                        <div class="person-dates">10.05.1982</div>
                        <div class="person-role">Я</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Татьяны и Владимира -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="tatyana-vladimir-1">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Татьяна Александровна</div>
                        <div class="person-role">Тетя</div>
                    </div>
                    <div class="person" data-person="vladimir-antyuganov">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Владимир Антюганов</div>
                        <div class="person-role">Дядя</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="svetlana-vladimirovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Светлана Владимировна</div>
                        <div class="person-dates">03.11.1975</div>
                        <div class="person-role">Двоюродная сестра</div>
                    </div>
                    <div class="person" data-person="andrey-vladimirovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Андрей Владимирович</div>
                        <div class="person-dates">28.01.1978</div>
                        <div class="person-role">Двоюродный брат</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Владимира Михайловича -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="vladimir-mikhaylovich-parent">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Владимир Михайлович</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="nina-vladimirovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Нина</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
            
            <!-- Семья Галины и Ивана -->
            <div class="family-branch">
                <div class="parents couple">
                    <div class="person" data-person="galina-ivan-1">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Галина Николаевна</div>
                        <div class="person-role">Мать</div>
                    </div>
                    <div class="person" data-person="ivan-gerasimovich">
                        <div class="person-icon male">👨</div>
                        <div class="person-name">Иван Герасимович</div>
                        <div class="person-dates">06.09.1936-03.10.2018</div>
                        <div class="person-role">Отец</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="anna-galinovna">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна</div>
                        <div class="person-role">Дочь</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Поколение 6: Внуки -->
        <div class="generation" id="gen6">
            <div class="generation-label">Поколение 6</div>
            <!-- Дети Нины -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="nina-vladimirovna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Нина</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="vadim-ninovich">
                        <div class="person-icon male">👶</div>
                        <div class="person-name">Вадим</div>
                        <div class="person-role">Сын</div>
                    </div>
                    <div class="person" data-person="anton-ninovich">
                        <div class="person-icon male">👶</div>
                        <div class="person-name">Антон</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
            
            <!-- Дети Анны -->
            <div class="family-branch">
                <div class="parents">
                    <div class="person" data-person="anna-galinovna-parent">
                        <div class="person-icon female">👩</div>
                        <div class="person-name">Анна</div>
                        <div class="person-role">Мать</div>
                    </div>
                </div>
                <div class="children">
                    <div class="person" data-person="mariya-annovna">
                        <div class="person-icon female">👶</div>
                        <div class="person-name">Мария</div>
                        <div class="person-role">Дочь</div>
                    </div>
                    <div class="person" data-person="anton-annovich">
                        <div class="person-icon male">👶</div>
                        <div class="person-name">Антон</div>
                        <div class="person-role">Сын</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- SVG для линий -->
        <svg class="tree-svg" id="tree-svg"></svg>
        
        <div style="margin-top: 60px; text-align: center; color: #7f8c8d; font-size: 14px; border-top: 2px solid #ecf0f1; padding-top: 20px;">
            Семейное древо составлено в 2025 году
        </div>
    </div>
    
    <script>
        function drawConnections() {
            const svg = document.getElementById('tree-svg');
            svg.innerHTML = '';
            
            function getPosition(selector) {
                const el = document.querySelector(`[data-person="${selector}"]`);
                if (!el) return null;
                const rect = el.getBoundingClientRect();
                const containerRect = document.querySelector('.tree-container').getBoundingClientRect();
                return {
                    x: rect.left + rect.width / 2 - containerRect.left,
                    y: rect.top + rect.height / 2 - containerRect.top
                };
            }
            
            function drawLine(x1, y1, x2, y2) {
                const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                line.setAttribute('x1', x1);
                line.setAttribute('y1', y1);
                line.setAttribute('x2', x2);
                line.setAttribute('y2', y2);
                line.setAttribute('class', 'connection-line');
                svg.appendChild(line);
            }
            
            // Рисуем линии внутри семейных блоков
            document.querySelectorAll('.family-branch').forEach(branch => {
                const parents = branch.querySelector('.parents');
                const children = branch.querySelector('.children');
                
                if (parents && children) {
                    const parentRect = parents.getBoundingClientRect();
                    const childrenRect = children.getBoundingClientRect();
                    const containerRect = document.querySelector('.tree-container').getBoundingClientRect();
                    
                    const parentX = parentRect.left + parentRect.width / 2 - containerRect.left;
                    const parentY = parentRect.bottom - containerRect.top;
                    const childrenX = childrenRect.left + childrenRect.width / 2 - containerRect.left;
                    const childrenY = childrenRect.top - containerRect.top;
                    
                    // Вертикальная линия от родителей к детям
                    drawLine(parentX, parentY, parentX, parentY + 20);
                    drawLine(parentX, parentY + 20, childrenX, parentY + 20);
                    drawLine(childrenX, parentY + 20, childrenX, childrenY);
                }
            });
        }
        
        window.onload = function() {
            setTimeout(drawConnections, 100);
            
            const button = document.createElement('button');
            button.textContent = 'Сохранить в PDF';
            button.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #3498db;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: bold;
                z-index: 1000;
                box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
            `;
            button.onclick = () => window.print();
            document.body.appendChild(button);
            
            window.addEventListener('resize', () => setTimeout(drawConnections, 100));
        };
    </script>
</body>
</html>
